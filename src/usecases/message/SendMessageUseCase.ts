import { UseCase } from '@/usecases/base/UseCase';
import { IMessageRepository, CreateMessageParams } from '@/infrastructure/MessageRepository';
import { IAIProvider } from '@/infrastructure/AIProvider';
import { IChatRepository } from '@/infrastructure/ChatRepository';
import { ProcessThinkingUseCase } from './ProcessThinkingUseCase';
import { UpdateMessageUseCase } from './UpdateMessageUseCase';
import { Msg, MessageContent, Chat, MessageStatus } from '@/core/types/chat';
import { v4 as uuid } from 'uuid';

export interface SendMessageInput {
  chatId: string;
  content: MessageContent;
  resend?: boolean;
  assistantMessageId?: string;
  // 回调函数，用于实时更新 UI
  onMessageUpdate?: (message: Msg) => void;
  onUserMessageCreated?: (message: Msg) => void;
  onAssistantMessageCreated?: (message: Msg) => void;
}

export interface SendMessageOutput {
  userMessage?: Msg;
  assistantMessage: Msg;
  success: boolean;
}

export class SendMessageUseCase extends UseCase<SendMessageInput, SendMessageOutput> {
  constructor(
    private messageRepo: IMessageRepository,
    private aiProvider: IAIProvider,
    private chatRepo: IChatRepository,
    private processThinking: ProcessThinkingUseCase,
    private updateMessage: UpdateMessageUseCase
  ) {
    super();
  }

  async execute(input: SendMessageInput): Promise<SendMessageOutput> {
    this.validateInput(input);

    const chat = await this.ensureChatExists(input.chatId);

    let userMessage: Msg | undefined;
    if (!input.resend) {
      userMessage = await this.messageRepo.create({
        chatId: chat.id,
        content: input.content,
        role: 'user',
      });

      // 通知 UI 层用户消息已创建
      if (input.onUserMessageCreated) {
        input.onUserMessageCreated(userMessage);
      }
    }

    const assistantMessage = await this.createOrUpdateAssistantMessage(input);

    // 通知 UI 层助手消息已创建
    if (input.onAssistantMessageCreated) {
      input.onAssistantMessageCreated(assistantMessage);
    }

    await this.handleAIResponse(chat.id, assistantMessage.id, input);

    return {
      userMessage,
      assistantMessage,
      success: true,
    };
  }

  private validateInput(input: SendMessageInput) {
    if (!input.chatId || input.chatId === '0') {
      throw new Error('No active session, cannot send message');
    }
  }

  private async ensureChatExists(chatId: string): Promise<Chat> {
    let chat = await this.chatRepo.findById(chatId);
    if (!chat) {
        // This part of the logic for creating a new chat needs to be clarified.
        // For now, we'll assume the chat exists.
        // In a real scenario, you might create a new chat here.
        throw new Error(`Chat with id ${chatId} not found.`);
    }
    return chat;
  }

  private async createOrUpdateAssistantMessage(input: SendMessageInput): Promise<Msg> {
    if (input.assistantMessageId) {
        const existingMsg = await this.messageRepo.findById(input.assistantMessageId);
        if(existingMsg) return existingMsg;
    }
    
    const assistantMessageParams: CreateMessageParams = {
        chatId: input.chatId,
        content: '',
        role: 'assistant',
    };

    return this.messageRepo.create(assistantMessageParams);
  }

  private async handleAIResponse(chatId: string, messageId: string, input: SendMessageInput) {
    try {
      const stream = await this.aiProvider.sendMessage({
        chatId,
        content: input.content,
        assistantMessageId: messageId,
        maxMessages: 10,
        resend: input.resend,
      });

      let fullContent = '';
      for await (const chunk of stream) {
        fullContent += chunk;

        // 处理思维链和更新消息
        const thinkingResult = await this.processThinking.execute({
          content: fullContent,
          messageId
        });

        await this.updateMessage.execute({
          messageId,
          content: thinkingResult.mainContent,
          thinkContent: thinkingResult.thinkContent,
          status: thinkingResult.status,
        });

        // 通知 UI 层消息已更新
        if (input.onMessageUpdate) {
          const updatedMessage = await this.messageRepo.findById(messageId);
          if (updatedMessage) {
            input.onMessageUpdate(updatedMessage);
          }
        }
      }

      // 流式处理完成后，设置最终状态
      await this.updateMessage.execute({
        messageId,
        status: MessageStatus.COMPLETED,
      });

      // 通知 UI 层消息最终完成
      if (input.onMessageUpdate) {
        const finalMessage = await this.messageRepo.findById(messageId);
        if (finalMessage) {
          input.onMessageUpdate(finalMessage);
        }
      }

    } catch (error) {
      console.error('AI response handling error:', error);

      // 设置错误状态
      await this.updateMessage.execute({
        messageId,
        status: MessageStatus.ERROR,
      });

      // 通知 UI 层消息出错
      if (input.onMessageUpdate) {
        const errorMessage = await this.messageRepo.findById(messageId);
        if (errorMessage) {
          input.onMessageUpdate(errorMessage);
        }
      }

      throw error;
    }
  }
}